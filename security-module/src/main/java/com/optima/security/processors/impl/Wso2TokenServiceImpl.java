package com.optima.security.processors.impl;

import com.optima.security.constants.TokenType;
import com.optima.security.exceptions.JwtTokenException;
import com.optima.security.model.JwtToken;
import com.optima.security.processors.TokenService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.util.Date;

import static java.util.Collections.emptyList;

@Service
public class Wso2TokenServiceImpl implements TokenService {

    @Value("${optima.security.token.wso2.secret}")
    private String wso2TokenSecret;

    @Value("${optima.security.token.wso2.expiration-time:#{60*30*1000}}")
    private Long tokenExpirationTime;

    @Override
    public AbstractAuthenticationToken process(<PERSON>laims claims) {
        if (claims.getSubject() != null) {
            JwtToken jwtToken = new JwtToken();
            jwtToken.setUid(claims.getSubject());
            jwtToken.setTokenType(TokenType.WSO2);
            jwtToken.setExpirationTime(claims.getExpiration());
            return new UsernamePasswordAuthenticationToken(jwtToken, null, emptyList());
        }
        return null;
    }

    @Override
    public TokenType getProcessorType() {
        return TokenType.WSO2;
    }

    @Override
    public String buildToken(Object o) throws JwtTokenException {
        if (o instanceof Wso2TokenRequest) {
            Wso2TokenRequest request = (Wso2TokenRequest) o;
            return Jwts.builder()
                    .claim("system", "selfcare")
                    .claim("user", request.getClientId())
                    .claim("ip", request.getIpAddress())
                    .setIssuedAt(new Date())
                    .setExpiration(new Date(System.currentTimeMillis() + tokenExpirationTime))
                    .signWith(SignatureAlgorithm.HS512, wso2TokenSecret)
                    .compact();
        }
        throw new JwtTokenException("Invalid object type for WSO2 token generation. Expected Wso2TokenRequest.");
    }

    @Override
    public String buildToken(Object o, String userId) throws JwtTokenException {
        return buildToken(o);
    }

    // Inner class for WSO2 token request parameters
    public static class Wso2TokenRequest {
        private final String clientId;
        private final String ipAddress;

        public Wso2TokenRequest(String clientId, String ipAddress) {
            this.clientId = clientId;
            this.ipAddress = ipAddress;
        }

        public String getClientId() {
            return clientId;
        }

        public String getIpAddress() {
            return ipAddress;
        }
    }
}
