package com.optima.security.configuration;

import com.optima.security.factory.TokenServiceFactory;
import com.optima.security.factory.impl.TokenServiceFactoryImpl;
import com.optima.security.processors.TokenService;
import com.optima.security.processors.impl.AdminTokenServiceImpl;
import com.optima.security.processors.impl.ProlongedTokenServiceImpl;
import com.optima.security.processors.impl.Wso2TokenServiceImpl;
import com.optima.security.service.JwtTokenService;
import com.optima.security.service.impl.JwtTokenServiceImpl;
import com.optima.security.service.impl.TokenAuthenticationService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class ServicesConfiguration {


    @Bean
    protected TokenAuthenticationService tokenAuthenticationService(JwtTokenService jwtTokenService) {
        return new TokenAuthenticationService(jwtTokenService);
    }

    @Bean
    protected TokenServiceFactory tokenServiceFactory(List<TokenService> list) {
        return new TokenServiceFactoryImpl(list);
    }

    @Bean
    protected JwtTokenService jwtTokenService(TokenServiceFactory tokenServiceFactory) {
        return new JwtTokenServiceImpl(tokenServiceFactory);
    }

    @Bean
    protected TokenService prolongedTokenService() {
        return new ProlongedTokenServiceImpl();
    }

    @Bean
    protected TokenService AdminTokenService() {

        return new AdminTokenServiceImpl();
    }
}
